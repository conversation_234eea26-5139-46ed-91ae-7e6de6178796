.alt-list li:nth-child(odd) {
    background-color: #f8f9fa;
  }
  .alt-list li:nth-child(even) {
    background-color: #e9ecef;
  }


  .dashboard-card {
    border-left: 5px solid #007bff;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    border-radius: 10px;
    background: white;
    transition: transform 0.2s;
}
.dashboard-card:hover {
    transform: scale(1.05);
}
.chart-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}
.list-group-item {
    border-left: 5px solid #28a745;
}



.icon-color
{
  color: #007bff;
}



.nav-link.active {
  /* background-color: #3282B8 !important;  */
  background-color: #1E3C72 !important; 
  color: white !important;
  font-weight: bold;
  border-radius: 5px;
}


/* custom primary button css  */
.btn-primary {
  background: linear-gradient(135deg, #1E3C72, #2A5298) !important;
  border: none !important;
  color: #fff !important;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #16345A, #1E3C72) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

